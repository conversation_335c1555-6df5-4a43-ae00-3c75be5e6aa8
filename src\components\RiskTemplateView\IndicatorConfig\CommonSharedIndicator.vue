<script setup lang="ts">
import { TRADE_DIRECTIONS } from '@/enum';
import { deepClone } from '@/script';
import type { CommonIndicatorConfig } from '@/types/riskc';
import { computed, reactive, useTemplateRef } from 'vue';
import type { RiskRule } from '../../../../../xtrade-sdk/dist';

import {
  RISK_TRIGGER_ACTIONS,
  RISK_TRIGGER_COMPARERS,
  RiskStepControls,
  RiskStepControl,
} from '@/enum/riskc';

const { contextRule } = defineProps<{ contextRule: RiskRule }>();
const formData = reactive<CommonIndicatorConfig>({
  name: '指标名称' + new Date().toDateString(),
  setting: {
    direction: 1,
    triggers: [
      {
        comparer: 1,
        threshold: 0,
        threholdUnit: '',
        action: 1,
      },
      {
        comparer: 1,
        threshold: 0,
        threholdUnit: '',
        action: 2,
      },
    ],
  },
  stepControl: {
    target: 2,
    frequency: 0,
  },
  effectiveDate: {
    start: '',
    end: '',
  },
  effectiveTime: {
    start: '',
    end: '',
  },
});

const rules = {
  name: [{ required: true, message: '请输入指标名字', trigger: 'blur' }],
  'setting.direction': [{ required: true, message: '请选择针对交易方向', trigger: 'blur' }],
  each_comparer: [{ required: true, message: '请选择比较符', trigger: 'blur' }],
  each_threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
  each_action: [{ required: true, message: '请选择处置方式', trigger: 'blur' }],
  'stepControl.target': [{ required: true, message: '请选择风控针对环节', trigger: 'blur' }],
  'stepControl.frequency': [{ required: true, message: '请指定风控频率', trigger: 'blur' }],
  'effectiveDate.start': [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
  'effectiveDate.end': [
    { required: true, message: '请输入结束日期', trigger: 'blur' },
    { validator: checkEndDate, trigger: 'blur' },
  ],
  'effectiveTime.start': [{ required: false, message: '请输入开始时间', trigger: 'blur' }],
  'effectiveTime.end': [
    { required: false, message: '请输入结束时间', trigger: 'blur' },
    { validator: checkEndTime, trigger: 'blur' },
  ],
};

const $form = useTemplateRef('$form');

function checkEndDate(rule: any, value: string, callback: (error?: Error) => void) {
  const { start, end } = formData.effectiveDate;
  if (start && end && end < start) {
    callback(new Error('结束日期，大于开始日期'));
  } else if (!end) {
    callback(new Error('请输入结束日期'));
  } else {
    callback();
  }
}

function checkEndTime(rule: any, value: string, callback: (error?: Error) => void) {
  const { start, end } = formData.effectiveTime;
  if (start && end && end <= start) {
    callback(new Error('结束时间，大于等于开始时间'));
  } else {
    callback();
  }
}

const handleValidate = () => {
  if (!$form.value) {
    return;
  }
  $form.value.validate().then(() => {
    handleSave();
  });
};

const handleSave = () => {
  console.log('保存成功', deepClone(formData));
};

const frequencyDesc = computed(() => {
  const { target, frequency } = formData.stepControl;
  if (frequency == 0) {
    return target == RiskStepControl.order.value ? '(0s表示委托单还未到柜台)' : '(0s表示事前检测)';
  }

  return '';
});
</script>

<template>
  <div class="view-idc-panel" h-full p-x-12>
    <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="指标名称" prop="name">
        <div class="name-row" w-full flex jcc aic gap-16>
          <el-input v-model.trim="formData.name" placeholder="请输入指标名称" clearable />
          <el-button type="primary" @click="handleValidate">保存</el-button>
        </div>
      </el-form-item>
      <el-form-item label="指标设置" prop="setting.direction">
        <el-select v-model="formData.setting.direction" placeholder="方向" style="width: 80px">
          <el-option
            v-for="(item, idx) in TRADE_DIRECTIONS"
            :key="idx"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <label class="placed-label" p-l-10>单笔最大委托量</label>
      </el-form-item>
      <div
        v-for="(trigger, idx) in formData.setting.triggers"
        :key="idx"
        class="custom-row"
        flex
        aic
        gap-10
      >
        <div w-170>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].comparer`"
            :rules="rules.each_comparer"
          >
            <el-select v-model="trigger.comparer">
              <el-option
                v-for="(item, idx) in RISK_TRIGGER_COMPARERS"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="post-item" w-110>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].threshold`"
            :rules="rules.each_threshold"
          >
            <el-input-number
              v-model="trigger.threshold"
              :controls="false"
              :precision="0"
              :min="0"
              placeholder="阈值"
              clearable
            />
          </el-form-item>
        </div>
        <label class="placed-label">万(股/份/张/手)时，</label>
        <div class="post-item" w-80>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].action`"
            :rules="rules.each_action"
          >
            <el-select v-model="trigger.action">
              <el-option
                v-for="(item, idx) in RISK_TRIGGER_ACTIONS"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="custom-row" flex aic gap-10>
        <div w-170>
          <el-form-item label="控制环节" prop="stepControl.target">
            <el-select v-model="formData.stepControl.target">
              <el-option
                v-for="(item, idx) in RiskStepControls"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="post-item">
          <el-input-number
            v-model="formData.stepControl.frequency"
            :controls="false"
            :precision="0"
            :min="0"
            :max="999"
            placeholder="间隔时长"
            style="width: 110px"
            clearable
          />
        </div>
        <label class="placed-label">秒执行一次{{ frequencyDesc }}</label>
      </div>
      <div class="custom-row" flex aic gap-16>
        <div w-270>
          <el-form-item label="生效日期" prop="effectiveDate.start">
            <el-date-picker
              v-model="formData.effectiveDate.start"
              type="date"
              value-format="YYYYMMDD"
              placeholder="开始日期"
              clearable
            />
          </el-form-item>
        </div>
        <label class="placed-label">至</label>
        <div w-190 class="post-item">
          <el-form-item label="" prop="effectiveDate.end">
            <el-date-picker
              v-model="formData.effectiveDate.end"
              type="date"
              value-format="YYYYMMDD"
              placeholder="结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
      <div class="custom-row" flex aic gap-16>
        <div w-270>
          <el-form-item label="生效时间" prop="effectiveTime.start">
            <el-time-picker
              v-model="formData.effectiveTime.start"
              value-format="hhmmss"
              placeholder="开始时间"
              clearable
            />
          </el-form-item>
        </div>
        <label class="placed-label">至</label>
        <div w-190 class="post-item">
          <el-form-item label="" prop="effectiveTime.end">
            <el-time-picker
              v-model="formData.effectiveTime.end"
              value-format="hhmmss"
              placeholder="结束时间"
              clearable
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.view-idc-panel {
  background-color: var(--g-block-bg-1);
  .placed-label {
    color: var(--g-text-color-2);
  }
  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }
  .name-row {
    :deep() {
      > .el-input {
        width: 100px;
        flex-grow: 1;
        flex-shrink: 1;
      }
    }
  }

  .custom-row {
    margin-bottom: 18px;
    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
