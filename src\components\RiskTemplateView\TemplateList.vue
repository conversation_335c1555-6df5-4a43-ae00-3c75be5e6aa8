<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { deleteConfirm } from '@/script/interaction';
import { Repos, type RiskTemplate } from '../../../../xtrade-sdk/dist';
import { getUser, remove } from '@/script';

const repoInstance = new Repos.RiskControlRepo();
const riskTemplates = ref<RiskTemplate[]>([]);
const searchKeyword = ref('');
const focusedId = ref<number | null>(null);

const dialogEdit = reactive({
  visible: false,
  name: '',
  current: null as RiskTemplate | null,
  isClone: false,
});

const title = computed(() => {
  const { current, isClone } = dialogEdit;
  return !current ? '添加风控模板' : isClone ? '模板克隆' : '风控模板重命名';
});

const filteredTemplates = computed(() => {
  const kw = (searchKeyword.value || '').toLowerCase();
  return riskTemplates.value.filter(template =>
    template.riskTemplateName.toLowerCase().includes(kw),
  );
});

const emitter = defineEmits<{
  select: [item: RiskTemplate | null];
  toggle: [];
}>();

const handleSelect = (target: RiskTemplate | null) => {
  focusedId.value = target ? target.id : null;
  emitter('select', target);
};

const handleEdit = (tmpl?: RiskTemplate, clone?: boolean) => {
  const obj = dialogEdit;
  obj.name = tmpl?.riskTemplateName || '';
  obj.visible = true;
  obj.current = tmpl || null;
  obj.isClone = !!clone;
};

const handleBind = (tmpl: RiskTemplate) => {
  ElMessage.error('功能暂未开发');
};

async function handleDelete(current: RiskTemplate) {
  const result = await deleteConfirm('删除模板', `确认删除此模板： ${current.riskTemplateName}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.DeleteTemplate(current.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    const tmpls = riskTemplates.value;
    remove(tmpls, x => x.id == current.id);
    const target = tmpls.length > 0 ? tmpls[0] : null;
    handleSelect(target);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

const handleToggle = () => {
  emitter('toggle');
};

const handleCancel = () => {
  closeDialog();
};

const closeDialog = () => {
  const obj = dialogEdit;
  obj.name = '';
  obj.visible = false;
  obj.current = null;
  obj.isClone = false;
};

const handleConfirm = async () => {
  const { name, current, isClone } = dialogEdit;
  if (!name) {
    ElMessage.error('请输入模板名称');
    return;
  }

  const is_creation = !current;
  const is_edit = !is_creation;
  const is_clone = is_edit && isClone;
  const matched_same_name = riskTemplates.value.filter(item => item.riskTemplateName === name);

  if (matched_same_name) {
    if (is_creation || is_clone) {
      ElMessage.error('该名称已存在');
      return;
    } else if (is_edit && current.riskTemplateName == name) {
      ElMessage.info('名称未变动');
      closeDialog();
      return;
    }
  }

  const user = getUser()!;
  const tmpl: RiskTemplate = {
    id: null as any,
    riskTemplateName: name,
    globalRiskTemplate: false,
    orgId: user.orgId,
    createTime: Date.now(),
    createUserId: user.userId,
    createUserName: user.username,
    updateTime: Date.now(),
  };

  if (is_edit) {
    Object.assign(tmpl, current);
    tmpl.riskTemplateName = name;
  }

  const resp = is_clone
    ? await repoInstance.CloneTemplate(tmpl.id)
    : is_creation
      ? await repoInstance.CreateTemplate(tmpl)
      : await repoInstance.UpdateTemplate(tmpl);

  const { errorCode, errorMsg } = resp;
  const behavior = is_clone ? '克隆' : is_edit ? '重命名' : '创建';

  if (errorCode == 0) {
    ElMessage.success(`已${behavior}`);
    closeDialog();
    request();
  } else {
    ElMessage.error(`${behavior}失败：${errorCode}/${errorMsg}`);
  }
};

async function request() {
  const list = (await repoInstance.QueryTemplates()).data || [];
  riskTemplates.value = list;
  const is_focused = focusedId.value && list.find(x => x.id == focusedId.value);

  if (list.length > 0 && !is_focused) {
    handleSelect(list[0]);
  }
}

function getCurrent() {
  return riskTemplates.value.find(x => x.id == focusedId.value);
}

defineExpose({
  getCurrent,
});

onMounted(() => {
  request();
});
</script>

<template>
  <div class="risk-template-list" p-10>
    <div class="search-box" flex aic gap-12>
      <!-- 搜索框 -->
      <el-input
        v-model.trim="searchKeyword"
        placeholder="搜索模板"
        :suffix-icon="Search"
        clearable
      />

      <!-- 添加按钮 -->
      <el-tooltip placement="top" content="添加模板">
        <i class="iconfont icon-add-new" thb1 @click="handleEdit()"></i>
      </el-tooltip>

      <!-- 隐藏显示按钮 -->
      <el-tooltip placement="top" content="隐藏|显示该列表">
        <i class="iconfont icon-exchange" thb1 @click="handleToggle"></i>
      </el-tooltip>
    </div>

    <!-- 风控模板列表 -->

    <div class="templite-list" mt-5>
      <template v-for="(tmpl, idx) in filteredTemplates" :key="idx">
        <div
          class="templite-item"
          :class="{ 'is-active': focusedId == tmpl.id }"
          h-40
          p-x-14
          flex
          jcsb
          aic
          gap-5
        >
          <div @click="handleSelect(tmpl)" h-full lh-40 flex-1 fs-14 fw-400>
            {{ tmpl.riskTemplateName }}
          </div>
          <el-popover
            popper-class="tmpl-oper-popover"
            trigger="click"
            placement="bottom"
            :width="150"
          >
            <template #reference>
              <div class="oper-ellipsis">
                <i class="iconfont icon-ellipsis" />
              </div>
            </template>
            <div class="riks-tmpl-oper-box" p-4 rd-8>
              <div class="oper-item" @click="handleEdit(tmpl)">
                <i class="iconfont icon-edit" />
                <span>重命名</span>
              </div>
              <div class="oper-item" @click="handleEdit(tmpl, true)">
                <i class="iconfont icon-copy" />
                <span>克隆模版</span>
              </div>
              <div class="oper-item" @click="handleBind(tmpl)">
                <i class="iconfont icon-consist-add" />
                <span>绑定产品</span>
              </div>
              <div class="oper-item" @click="handleDelete(tmpl)">
                <i class="iconfont icon-remove" />
                <span>删除模版</span>
              </div>
            </div>
          </el-popover>
        </div>
      </template>
    </div>

    <!-- 添加模板对话框 -->
    <el-dialog v-model="dialogEdit.visible" :title="title" width="300px" draggable>
      <el-input v-model.trim="dialogEdit.name" placeholder="请输入风控模板名称" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.risk-template-list {
  .templite-list {
    .templite-item {
      &:hover,
      &.is-active {
        background-color: var(--g-block-bg-6);
      }
    }
    .oper-ellipsis {
      padding: 3px;
      border-radius: 4px;
      opacity: 0.5;
      &:hover {
        background-color: var(--g-bg-hover-1);
        opacity: 1;
      }
    }
  }
}
</style>

<style>
.tmpl-oper-popover {
  padding: 4px !important;
  min-width: 100px !important;
}

.riks-tmpl-oper-box {
  background-color: var(--g-bg);
  .oper-item {
    height: 40px;
    padding-left: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 4px;
    &:hover {
      background-color: var(--g-block-bg-6);
    }
  }
}
</style>
