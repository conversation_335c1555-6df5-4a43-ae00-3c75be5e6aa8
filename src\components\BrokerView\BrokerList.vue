<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import BrokerDialog from './BrokerDialog.vue';
import { onMounted, shallowRef, useTemplateRef, computed } from 'vue';
import { TableV2SortOrder, ElMessage, ElMessageBox } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { formatDateTime, hasPermission } from '@/script';
import { type MomBroker, BrokerTypeEnum } from '../../../../xtrade-sdk/dist';
import { MenuPermitBrokerManagement } from '@/enum';

// 基础列定义
const columns: ColumnDefinition<MomBroker> = [
  { key: 'brokerName', title: '经纪商名称', width: 200, sortable: true },
  { key: 'brokerId', title: '经纪商代码', width: 200, sortable: true },
  {
    key: 'brokerType',
    title: '经纪商类型',
    width: 200,
    sortable: true,
    cellRenderer: formatBrokerType,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDate,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDate,
  },
];

// 权限判断
const canCreate = computed(() => hasPermission(MenuPermitBrokerManagement.创建));
const canEdit = computed(() => hasPermission(MenuPermitBrokerManagement.编辑));
const canDelete = computed(() => hasPermission(MenuPermitBrokerManagement.删除));

// 行操作
const rowActions: RowAction<MomBroker>[] = [
  {
    label: '编辑',
    icon: 'edit',
    show: () => canEdit.value,
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: () => canDelete.value,
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = shallowRef<MomBroker[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = shallowRef(false);
const editingBroker = shallowRef<MomBroker | undefined>();

function formatDate(params: any) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// 格式化经纪商类型
function formatBrokerType({ cellData }: { cellData: BrokerTypeEnum }) {
  return <span>{BrokerTypeEnum[cellData]}</span>;
}

// 新建经纪商
const handleCreate = () => {
  editingBroker.value = undefined;
  dialogVisible.value = true;
};

// 编辑经纪商
function editRow(row: MomBroker) {
  editingBroker.value = row;
  dialogVisible.value = true;
}

// 删除经纪商
function deleteRow(row: MomBroker) {
  ElMessageBox.confirm(`确定要删除经纪商"${row.brokerName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const { errorCode, errorMsg } = await AdminService.deleteBroker(row.id);
        if (errorCode === 0) {
          ElMessage.success('删除成功');
          await request();
        } else {
          ElMessage.error(errorMsg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  try {
    records.value = await AdminService.getBrokers();
  } catch (error) {
    console.error('获取经纪商列表失败:', error);
    ElMessage.error('获取经纪商列表失败');
  }
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <!-- <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button> -->
        <el-button v-if="canCreate" type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建经纪商</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 经纪商编辑对话框 -->
  <BrokerDialog v-model="dialogVisible" :broker="editingBroker" @success="handleDialogSuccess" />
</template>

<style scoped></style>
