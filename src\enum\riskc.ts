import { enumToArray } from '@/script';

/**
 * 触发风控阈值比较符
 */
export enum RiskTriggerComparer {
  '>' = 1,
  '<' = 2,
}

/**
 * 触发风控阈值比较符
 */
export const RISK_TRIGGER_COMPARERS = enumToArray(RiskTriggerComparer);

/**
 * 触发风控采取动作
 */
export enum RiskTriggerAction {
  预警 = 1,
  阻止 = 2,
}

/**
 * 触发风控采取动作
 */
export const RISK_TRIGGER_ACTIONS = enumToArray(RiskTriggerAction);

/**
 * 风控环节控制
 */
export const RiskStepControl = {
  instruction: { label: '针对指令', value: 1 },
  order: { label: '针对委托', value: 2 },
  instruction_and_order: { label: '针对指令+委托', value: 3 },
};

/**
 * 风控环节控制
 */
export const RiskStepControls = Object.values(RiskStepControl);
