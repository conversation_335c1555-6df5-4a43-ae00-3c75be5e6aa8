<script setup lang="tsx">
import { shallowRef, watch, computed } from 'vue';
import type { MomActionLog, MomUser } from '../../../../xtrade-sdk/dist';
import type { ColumnDefinition } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { AdminService } from '@/api';
import { ActionLogTypeEnum } from '../../../../xtrade-sdk/dist';
import { formatDateTime } from '@/script';

const { user, visible, logType } = defineProps<{
  user?: MomUser;
  visible: boolean;
  logType: ActionLogTypeEnum;
}>();

// 日志数据
const logs = shallowRef<MomActionLog[]>([]);

// 根据日志类型确定显示的列
const columns = computed<ColumnDefinition<MomActionLog>>(() => {
  const baseColumns: ColumnDefinition<MomActionLog> = [
    {
      key: 'updateTime',
      title: '时间',
      width: 160,
      sortable: true,
      cellRenderer: ({ cellData }) => <span>{formatDateTime(cellData)}</span>,
    },
    {
      key: 'actionName',
      title: '操作名称',
      width: 150,
      sortable: true,
    },
    {
      key: 'ip',
      title: 'IP',
      width: 120,
      sortable: true,
    },
    {
      key: 'parameter',
      title: '参数',
      width: 200,
      sortable: true,
      cellRenderer: ({ cellData }) => <span>{cellData || '--'}</span>,
    },
    {
      key: 'os',
      title: 'OS',
      width: 200,
      sortable: true,
    },
  ];

  // 操作日志需要额外显示结果列
  if (logType === ActionLogTypeEnum.操作日志) {
    baseColumns.push({
      key: 'result',
      title: '结果',
      width: 150,
      sortable: true,
      cellRenderer: ({ cellData }) => <span>{cellData || '--'}</span>,
    });
  }

  return baseColumns;
});

// 获取日志数据
const fetchLogs = async () => {
  if (!user?.id) return;

  try {
    const { errorCode, data } = await AdminService.getActionLogs(logType, user.id);
    if (errorCode === 0 && data) {
      logs.value = data;
    } else {
      logs.value = [];
    }
  } catch (error) {
    console.error('获取日志失败:', error);
    logs.value = [];
  }
};

// 监听visible和user变化，重新获取数据
watch(
  [() => visible, () => user?.id, () => logType],
  ([newVisible, newUserId]) => {
    if (newVisible && newUserId) {
      fetchLogs();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div>
    <VirtualizedTable :columns="columns" :data="logs" />
  </div>
</template>

<style scoped></style>
