import { BaseRepo } from '../modules/base-repo';
import { KindOfAsset, RiskIndicator, RiskRule, RiskTemplate } from '../types/table/risk-control';

const BaseUrl = '../v4/risk';

export class RiskControlRepo extends BaseRepo {

    constructor() {
        super();
    }

    /**
     * 查询所有风控模板
     */
    async QueryTemplates() {
        return await this.assist.Get<RiskTemplate[]>(`${BaseUrl}/template/all`);
    }

    /**
     * 创建风控模板
     */
    async CreateTemplate(template: RiskTemplate) {
        return await this.assist.Post<[]>(`${BaseUrl}/template`, {}, template);
    }

    /**
     * 更新风控模板
     */
    async UpdateTemplate(template: RiskTemplate) {
        return await this.assist.Put<[]>(`${BaseUrl}/template`, {}, template);
    }

    /**
     * 克隆风控模板
     */
    async CloneTemplate(template_id: number) {
        return await this.assist.Get<[]>(`${BaseUrl}/template/clone`, { template_id });
    }

    /**
     * 删除风控模板
     */
    async DeleteTemplate(id: number) {
        return await this.assist.Delete<[]>(`${BaseUrl}/template`, { id });
    }

    /**
     * 查询所有风控指标
     */
    async QueryIndicators() {
        return await this.assist.Get<RiskIndicator[]>(`${BaseUrl}/indicator/all`);
    }

    /**
     * 查询所有风控规则
     */
    async QueryRules() {
        return await this.assist.Get<RiskRule[]>(`${BaseUrl}/rule/all`);
    }

    /**
     * 创建风控规则
     */
    async CreateRule(rule: RiskRule) {
        return await this.assist.Post<[]>(`${BaseUrl}/rule`, {}, rule);
    }

    /**
     * 更新风控规则
     */
    async UpdateRule(rule: RiskRule) {
        return await this.assist.Put<[]>(`${BaseUrl}/rule`, {}, rule);
    }

    /**
     * 删除风控规则
     */
    async DeleteRule(id: number) {
        return await this.assist.Delete<[]>(`${BaseUrl}/rule`, { id });
    }

    /**
     * 查询资产范围
     */
    async QueryAssetScopes() {
        return await this.assist.Get<KindOfAsset[]>(`${BaseUrl}/kind/all`);
    }
}
